"""
页面导航操作模块
提供页面导航相关的基础操作
"""
import time
from typing import Optional
from logger.logger import get_logger
from ...utils import wait_for_page_load, take_screenshot


class NavigateAction:
    """页面导航操作类"""

    def __init__(self, tab):
        """
        初始化页面导航操作

        Args:
            tab: 浏览器标签页
        """
        self.tab = tab
        self.logger = get_logger("NavigateAction")

    def goto(self, url: str, wait_load: bool = True, timeout: int = 30) -> bool:
        """
        导航到指定URL
        
        Args:
            url (str): 目标URL
            wait_load (bool): 是否等待页面加载完成
            timeout (int): 页面加载超时时间
            
        Returns:
            bool: 是否导航成功
        """
        try:
            self.logger.info(f"导航到: {url}")
            self.tab.get(url)

            if wait_load:
                success = wait_for_page_load(self.tab, timeout)
                if not success:
                    self.logger.warning(f"页面加载超时: {url}")
                    return False

            # 短暂等待确保页面稳定
            time.sleep(1)
            self.logger.info(f"成功导航到: {url}")
            return True
        except Exception as e:
            self.logger.error(f"导航失败: {e}")
            return False

    def refresh(self, wait_load: bool = True, timeout: int = 30) -> bool:
        """
        刷新当前页面
        
        Args:
            wait_load (bool): 是否等待页面加载完成
            timeout (int): 页面加载超时时间
            
        Returns:
            bool: 是否刷新成功
        """
        try:
            self.logger.info("刷新页面")
            self.tab.refresh()

            if wait_load:
                success = wait_for_page_load(self.tab, timeout)
                if not success:
                    self.logger.warning("页面刷新后加载超时")
                    return False

            time.sleep(1)
            self.logger.info("页面刷新成功")
            return True
        except Exception as e:
            self.logger.error(f"页面刷新失败: {e}")
            return False

    def back(self, wait_load: bool = True, timeout: int = 30) -> bool:
        """
        返回上一页
        
        Args:
            wait_load (bool): 是否等待页面加载完成
            timeout (int): 页面加载超时时间
            
        Returns:
            bool: 是否返回成功
        """
        try:
            self.logger.info("返回上一页")
            self.tab.back()

            if wait_load:
                success = wait_for_page_load(self.tab, timeout)
                if not success:
                    self.logger.warning("返回上一页后加载超时")
                    return False

            time.sleep(1)
            self.logger.info("成功返回上一页")
            return True
        except Exception as e:
            self.logger.error(f"返回上一页失败: {e}")
            return False

    def forward(self, wait_load: bool = True, timeout: int = 30) -> bool:
        """
        前进到下一页
        
        Args:
            wait_load (bool): 是否等待页面加载完成
            timeout (int): 页面加载超时时间
            
        Returns:
            bool: 是否前进成功
        """
        try:
            self.logger.info("前进到下一页")
            self.tab.forward()

            if wait_load:
                success = wait_for_page_load(self.tab, timeout)
                if not success:
                    self.logger.warning("前进到下一页后加载超时")
                    return False

            time.sleep(1)
            self.logger.info("成功前进到下一页")
            return True
        except Exception as e:
            self.logger.error(f"前进到下一页失败: {e}")
            return False

    def get_current_url(self) -> Optional[str]:
        """
        获取当前页面URL
        
        Returns:
            str: 当前页面URL，获取失败返回None
        """
        try:
            url = self.tab.url
            self.logger.debug(f"当前页面URL: {url}")
            return url
        except Exception as e:
            self.logger.error(f"获取当前URL失败: {e}")
            return None

    def get_page_title(self) -> Optional[str]:
        """
        获取页面标题
        
        Returns:
            str: 页面标题，获取失败返回None
        """
        try:
            title = self.tab.title
            self.logger.debug(f"页面标题: {title}")
            return title
        except Exception as e:
            self.logger.error(f"获取页面标题失败: {e}")
            return None

    def wait_for_url_change(self, current_url: str, timeout: int = 30, 
                           check_interval: float = 0.5) -> bool:
        """
        等待URL发生变化
        
        Args:
            current_url (str): 当前URL
            timeout (int): 超时时间
            check_interval (float): 检查间隔
            
        Returns:
            bool: URL是否发生变化
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            new_url = self.get_current_url()
            if new_url and new_url != current_url:
                self.logger.info(f"URL已变化: {current_url} -> {new_url}")
                return True
            time.sleep(check_interval)

        self.logger.warning(f"等待URL变化超时: {current_url}")
        return False

    def wait_for_url_contains(self, text: str, timeout: int = 30, 
                             check_interval: float = 0.5) -> bool:
        """
        等待URL包含指定文本
        
        Args:
            text (str): 要包含的文本
            timeout (int): 超时时间
            check_interval (float): 检查间隔
            
        Returns:
            bool: URL是否包含指定文本
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            current_url = self.get_current_url()
            if current_url and text in current_url:
                self.logger.info(f"URL包含指定文本: {text}")
                return True
            time.sleep(check_interval)

        self.logger.warning(f"等待URL包含文本超时: {text}")
        return False

    def scroll_to_top(self) -> bool:
        """
        滚动到页面顶部
        
        Returns:
            bool: 是否滚动成功
        """
        try:
            self.tab.scroll.to_top()
            time.sleep(0.5)
            self.logger.info("已滚动到页面顶部")
            return True
        except Exception as e:
            self.logger.error(f"滚动到页面顶部失败: {e}")
            return False

    def scroll_to_bottom(self) -> bool:
        """
        滚动到页面底部
        
        Returns:
            bool: 是否滚动成功
        """
        try:
            self.tab.scroll.to_bottom()
            time.sleep(0.5)
            self.logger.info("已滚动到页面底部")
            return True
        except Exception as e:
            self.logger.error(f"滚动到页面底部失败: {e}")
            return False

    def scroll_by_pixels(self, x: int = 0, y: int = 0) -> bool:
        """
        按像素滚动页面
        
        Args:
            x (int): 水平滚动像素数
            y (int): 垂直滚动像素数
            
        Returns:
            bool: 是否滚动成功
        """
        try:
            self.tab.scroll(x, y)
            time.sleep(0.3)
            self.logger.debug(f"页面滚动: x={x}, y={y}")
            return True
        except Exception as e:
            self.logger.error(f"页面滚动失败: {e}")
            return False

    def take_screenshot(self, filename: str = None, save_config: bool = True) -> Optional[str]:
        """
        截取页面截图
        
        Args:
            filename (str, optional): 截图文件名
            save_config (bool): 是否根据配置决定是否保存
            
        Returns:
            str: 截图文件路径，失败或配置不允许时返回None
        """
        if not save_config:
            # TODO: 从配置文件读取是否允许截图
            # 暂时默认允许截图
            pass
        
        return take_screenshot(self.tab, filename)

    def close_tab(self) -> bool:
        """
        关闭当前标签页
        
        Returns:
            bool: 是否关闭成功
        """
        try:
            self.tab.close()
            self.logger.info("已关闭标签页")
            return True
        except Exception as e:
            self.logger.error(f"关闭标签页失败: {e}")
            return False
